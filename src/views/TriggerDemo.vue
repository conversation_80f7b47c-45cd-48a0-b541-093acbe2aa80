<script setup lang="ts">
import { ref } from 'vue';
import { Trigger } from '@xiaou66/u-web-ui';

const visible1 = ref(false);
const visible2 = ref(false);
const visible3 = ref(false);

const handleVisibleChange = (visible: boolean, name: string) => {
  console.log(`${name} 可见状态改变:`, visible);
};

const handleShow = (name: string) => {
  console.log(`${name} 显示`);
};

const handleHide = (name: string) => {
  console.log(`${name} 隐藏`);
};
</script>

<template>
  <div class="demo-container">
    <h1>Trigger 触发器组件演示</h1>

    <div class="demo-section">
      <h2>基础用法</h2>
      <p>支持 hover、click、focus、contextMenu 等触发方式</p>

      <div class="demo-row">
        <div class="demo-item">
          <h3>Hover 触发</h3>
          <Trigger
            trigger="hover"
            position="top"
            @popup-visible-change="(visible) => handleVisibleChange(visible, 'Hover')"
            @show="() => handleShow('Hover')"
            @hide="() => handleHide('Hover')"
          >
            <button class="demo-button">Hover Me</button>
            <template #content>
              <div class="demo-content">
                <p>这是通过 hover 触发的弹出框</p>
                <p>鼠标移入显示，移出隐藏</p>
              </div>
            </template>
          </Trigger>
        </div>

        <div class="demo-item">
          <h3>Click 触发</h3>
          <Trigger
            trigger="click"
            position="bottom"
            @popup-visible-change="(visible) => handleVisibleChange(visible, 'Click')"
          >
            <button class="demo-button">Click Me</button>
            <template #content>
              <div class="demo-content">
                <p>这是通过 click 触发的弹出框</p>
                <p>点击按钮切换显示/隐藏</p>
              </div>
            </template>
          </Trigger>
        </div>

        <div class="demo-item">
          <h3>Focus 触发</h3>
          <Trigger
            trigger="focus"
            position="right"
          >
            <input class="demo-input" placeholder="Focus on me" />
            <template #content>
              <div class="demo-content">
                <p>这是通过 focus 触发的弹出框</p>
                <p>获得焦点时显示，失去焦点时隐藏</p>
              </div>
            </template>
          </Trigger>
        </div>

        <div class="demo-item">
          <h3>右键菜单</h3>
          <Trigger
            trigger="contextMenu"
            position="bottom-start"
          >
            <div class="demo-context-area">右键点击这里</div>
            <template #content>
              <div class="demo-menu">
                <div class="menu-item">复制</div>
                <div class="menu-item">粘贴</div>
                <div class="menu-item">删除</div>
              </div>
            </template>
          </Trigger>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>弹出位置</h2>
      <p>支持 12 个方向的弹出位置</p>

      <div class="position-demo">
        <div class="position-row">
          <Trigger position="top-start">
            <button class="position-btn">TL</button>
            <template #content>
              <div class="demo-content">Top Start</div>
            </template>
          </Trigger>
          <Trigger position="top">
            <button class="position-btn">Top</button>
            <template #content>
              <div class="demo-content">Top</div>
            </template>
          </Trigger>
          <Trigger position="top-end">
            <button class="position-btn">TR</button>
            <template #content>
              <div class="demo-content">Top End</div>
            </template>
          </Trigger>
        </div>

        <div class="position-row">
          <Trigger position="left-start">
            <button class="position-btn">LT</button>
            <template #content>
              <div class="demo-content">Left Start</div>
            </template>
          </Trigger>
          <div class="position-center">
            <span>Trigger</span>
          </div>
          <Trigger position="right-start">
            <button class="position-btn">RT</button>
            <template #content>
              <div class="demo-content">Right Start</div>
            </template>
          </Trigger>
        </div>

        <div class="position-row">
          <Trigger position="left">
            <button class="position-btn">Left</button>
            <template #content>
              <div class="demo-content">Left</div>
            </template>
          </Trigger>
          <div class="position-center"></div>
          <Trigger position="right">
            <button class="position-btn">Right</button>
            <template #content>
              <div class="demo-content">Right</div>
            </template>
          </Trigger>
        </div>

        <div class="position-row">
          <Trigger position="left-end">
            <button class="position-btn">LB</button>
            <template #content>
              <div class="demo-content">Left End</div>
            </template>
          </Trigger>
          <div class="position-center"></div>
          <Trigger position="right-end">
            <button class="position-btn">RB</button>
            <template #content>
              <div class="demo-content">Right End</div>
            </template>
          </Trigger>
        </div>

        <div class="position-row">
          <Trigger position="bottom-start">
            <button class="position-btn">BL</button>
            <template #content>
              <div class="demo-content">Bottom Start</div>
            </template>
          </Trigger>
          <Trigger position="bottom">
            <button class="position-btn">Bottom</button>
            <template #content>
              <div class="demo-content">Bottom</div>
            </template>
          </Trigger>
          <Trigger position="bottom-end">
            <button class="position-btn">BR</button>
            <template #content>
              <div class="demo-content">Bottom End</div>
            </template>
          </Trigger>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>带箭头</h2>
      <p>可以显示指向触发元素的箭头</p>

      <div class="demo-row">
        <Trigger
          trigger="hover"
          position="top"
          :show-arrow="true"
        >
          <button class="demo-button">Top Arrow</button>
          <template #content>
            <div class="demo-content">
              <p>带箭头的弹出框</p>
            </div>
          </template>
        </Trigger>

        <Trigger
          trigger="hover"
          position="right"
          :show-arrow="true"
        >
          <button class="demo-button">Right Arrow</button>
          <template #content>
            <div class="demo-content">
              <p>右侧箭头</p>
            </div>
          </template>
        </Trigger>

        <Trigger
          trigger="hover"
          position="bottom"
          :show-arrow="true"
        >
          <button class="demo-button">Bottom Arrow</button>
          <template #content>
            <div class="demo-content">
              <p>底部箭头</p>
            </div>
          </template>
        </Trigger>

        <Trigger
          trigger="hover"
          position="left"
          :show-arrow="true"
        >
          <button class="demo-button">Left Arrow</button>
          <template #content>
            <div class="demo-content">
              <p>左侧箭头</p>
            </div>
          </template>
        </Trigger>
      </div>
    </div>

    <div class="demo-section">
      <h2>受控模式</h2>
      <p>通过 v-model:popup-visible 控制弹出框的显示状态</p>

      <div class="demo-row">
        <div class="demo-item">
          <div class="control-buttons">
            <button @click="visible1 = true" class="control-btn">显示</button>
            <button @click="visible1 = false" class="control-btn">隐藏</button>
            <button @click="visible1 = !visible1" class="control-btn">切换</button>
          </div>

          <Trigger
            v-model:popup-visible="visible1"
            trigger="click"
            position="bottom"
          >
            <button class="demo-button">受控触发器</button>
            <template #content>
              <div class="demo-content">
                <p>这是受控模式的弹出框</p>
                <p>状态: {{ visible1 ? '显示' : '隐藏' }}</p>
              </div>
            </template>
          </Trigger>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>动画效果</h2>
      <p>支持多种动画效果</p>

      <div class="demo-row">
        <Trigger
          trigger="hover"
          position="top"
          animation-name="fade"
        >
          <button class="demo-button">Fade</button>
          <template #content>
            <div class="demo-content">淡入淡出动画</div>
          </template>
        </Trigger>

        <Trigger
          trigger="hover"
          position="top"
          animation-name="zoom"
        >
          <button class="demo-button">Zoom</button>
          <template #content>
            <div class="demo-content">缩放动画</div>
          </template>
        </Trigger>

        <Trigger
          trigger="hover"
          position="top"
          animation-name="slide-up"
        >
          <button class="demo-button">Slide Up</button>
          <template #content>
            <div class="demo-content">向上滑动</div>
          </template>
        </Trigger>

        <Trigger
          trigger="hover"
          position="bottom"
          animation-name="slide-down"
        >
          <button class="demo-button">Slide Down</button>
          <template #content>
            <div class="demo-content">向下滑动</div>
          </template>
        </Trigger>
      </div>
    </div>

    <div class="demo-section">
      <h2>延迟和配置</h2>
      <p>可以配置鼠标进入/离开的延迟时间</p>

      <div class="demo-row">
        <Trigger
          trigger="hover"
          position="top"
          :mouse-enter-delay="500"
          :mouse-leave-delay="200"
        >
          <button class="demo-button">延迟触发</button>
          <template #content>
            <div class="demo-content">
              <p>鼠标进入延迟 500ms</p>
              <p>鼠标离开延迟 200ms</p>
            </div>
          </template>
        </Trigger>

        <Trigger
          trigger="hover"
          position="top"
          :popup-offset="20"
        >
          <button class="demo-button">自定义偏移</button>
          <template #content>
            <div class="demo-content">
              <p>距离触发器 20px</p>
            </div>
          </template>
        </Trigger>

        <Trigger
          trigger="click"
          position="bottom"
          :disabled="true"
        >
          <button class="demo-button disabled">禁用状态</button>
          <template #content>
            <div class="demo-content">
              <p>这个不会显示</p>
            </div>
          </template>
        </Trigger>
      </div>
    </div>

    <div class="demo-section">
      <h2>复杂内容</h2>
      <p>弹出框可以包含复杂的内容</p>

      <div class="demo-row">
        <Trigger
          trigger="click"
          position="bottom-start"
          :popup-hover-stay="true"
        >
          <button class="demo-button">用户信息</button>
          <template #content>
            <div class="user-card">
              <div class="user-avatar">👤</div>
              <div class="user-info">
                <h4>张三</h4>
                <p>前端开发工程师</p>
                <div class="user-actions">
                  <button class="action-btn">发消息</button>
                  <button class="action-btn">查看资料</button>
                </div>
              </div>
            </div>
          </template>
        </Trigger>

        <Trigger
          trigger="click"
          position="bottom-start"
          :show-arrow="true"
        >
          <button class="demo-button">表单弹框</button>
          <template #content>
            <div class="form-popup">
              <h4>快速添加</h4>
              <div class="form-item">
                <label>名称:</label>
                <input type="text" placeholder="请输入名称" />
              </div>
              <div class="form-item">
                <label>描述:</label>
                <textarea placeholder="请输入描述"></textarea>
              </div>
              <div class="form-actions">
                <button class="action-btn primary">确定</button>
                <button class="action-btn">取消</button>
              </div>
            </div>
          </template>
        </Trigger>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-section {
  margin-bottom: 40px;

  h1 {
    color: var(--u-text-color-primary, #333);
    margin-bottom: 20px;
    font-size: 28px;
    font-weight: 600;
  }

  h2 {
    margin-bottom: 16px;
    color: var(--u-text-color-primary, #333);
    border-bottom: 2px solid var(--u-color-neutral-3, #e0e0e0);
    padding-bottom: 8px;
    font-size: 20px;
    font-weight: 500;
  }

  h3 {
    margin-bottom: 12px;
    color: var(--u-text-color-secondary, #666);
    font-size: 16px;
    font-weight: 500;
  }

  p {
    margin-bottom: 16px;
    color: var(--u-text-color-secondary, #666);
    line-height: 1.6;
  }
}

.demo-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  align-items: flex-start;
}

.demo-item {
  flex: 1;
  min-width: 200px;
}

.demo-button {
  padding: 8px 16px;
  background: var(--u-bg-color-3, #ffffff);
  border: 1px solid var(--u-color-neutral-3, #e0e0e0);
  border-radius: var(--u-radius-default, 6px);
  color: var(--u-text-color-primary, #333);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;

  &:hover:not(.disabled) {
    background: var(--u-bg-color-3-hover, #f5f5f5);
    border-color: var(--u-text-color-brand, #1890ff);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.demo-input {
  padding: 8px 12px;
  border: 1px solid var(--u-color-neutral-3, #e0e0e0);
  border-radius: var(--u-radius-default, 6px);
  font-size: 14px;
  width: 200px;

  &:focus {
    outline: none;
    border-color: var(--u-text-color-brand, #1890ff);
  }
}

.demo-context-area {
  width: 200px;
  height: 100px;
  background: var(--u-bg-color-2, #f8f9fa);
  border: 2px dashed var(--u-color-neutral-3, #e0e0e0);
  border-radius: var(--u-radius-default, 6px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--u-text-color-secondary, #666);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--u-text-color-brand, #1890ff);
    background: var(--u-bg-color-3-hover, #f0f8ff);
  }
}

.demo-content {
  min-width: 200px;

  p {
    margin: 0 0 8px 0;
    font-size: 14px;
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.demo-menu {
  min-width: 120px;

  .menu-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--u-bg-color-3-hover, #f5f5f5);
    }

    &:not(:last-child) {
      border-bottom: 1px solid var(--u-color-neutral-2, #f0f0f0);
    }
  }
}

// 位置演示样式
.position-demo {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  padding: 40px;
  background: var(--u-bg-color-2, #f8f9fa);
  border-radius: var(--u-radius-medium, 8px);
  margin: 20px 0;
}

.position-row {
  display: flex;
  gap: 16px;
  align-items: center;
}

.position-btn {
  width: 60px;
  height: 32px;
  padding: 4px 8px;
  background: var(--u-bg-color-3, #ffffff);
  border: 1px solid var(--u-color-neutral-3, #e0e0e0);
  border-radius: var(--u-radius-default, 6px);
  color: var(--u-text-color-primary, #333);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;

  &:hover {
    background: var(--u-bg-color-3-hover, #f5f5f5);
    border-color: var(--u-text-color-brand, #1890ff);
  }
}

.position-center {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--u-text-color-brand, #1890ff);
  color: white;
  border-radius: var(--u-radius-default, 6px);
  font-size: 14px;
  font-weight: 500;
}

// 控制按钮样式
.control-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.control-btn {
  padding: 6px 12px;
  background: var(--u-text-color-brand, #1890ff);
  color: white;
  border: none;
  border-radius: var(--u-radius-default, 6px);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;

  &:hover {
    background: var(--u-text-color-hover, #0052d9);
  }
}

// 用户卡片样式
.user-card {
  display: flex;
  gap: 12px;
  min-width: 280px;
  padding: 4px;

  .user-avatar {
    width: 48px;
    height: 48px;
    background: var(--u-bg-color-2, #f8f9fa);
    border-radius: var(--u-radius-circle, 50%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
  }

  .user-info {
    flex: 1;

    h4 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 500;
      color: var(--u-text-color-primary, #333);
    }

    p {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: var(--u-text-color-secondary, #666);
    }
  }

  .user-actions {
    display: flex;
    gap: 8px;
  }
}

// 表单弹框样式
.form-popup {
  min-width: 300px;

  h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--u-text-color-primary, #333);
  }

  .form-item {
    margin-bottom: 16px;

    label {
      display: block;
      margin-bottom: 4px;
      font-size: 14px;
      color: var(--u-text-color-primary, #333);
    }

    input, textarea {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid var(--u-color-neutral-3, #e0e0e0);
      border-radius: var(--u-radius-default, 6px);
      font-size: 14px;
      box-sizing: border-box;

      &:focus {
        outline: none;
        border-color: var(--u-text-color-brand, #1890ff);
      }
    }

    textarea {
      height: 80px;
      resize: vertical;
    }
  }

  .form-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
}

// 通用按钮样式
.action-btn {
  padding: 6px 12px;
  border: 1px solid var(--u-color-neutral-3, #e0e0e0);
  border-radius: var(--u-radius-default, 6px);
  background: var(--u-bg-color-3, #ffffff);
  color: var(--u-text-color-primary, #333);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;

  &:hover {
    background: var(--u-bg-color-3-hover, #f5f5f5);
    border-color: var(--u-text-color-brand, #1890ff);
  }

  &.primary {
    background: var(--u-text-color-brand, #1890ff);
    color: white;
    border-color: var(--u-text-color-brand, #1890ff);

    &:hover {
      background: var(--u-text-color-hover, #0052d9);
      border-color: var(--u-text-color-hover, #0052d9);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .demo-container {
    padding: 16px;
  }

  .demo-row {
    flex-direction: column;
    gap: 16px;
  }

  .position-demo {
    padding: 20px;
  }

  .position-row {
    gap: 12px;
  }

  .position-btn {
    width: 50px;
    height: 28px;
    font-size: 11px;
  }

  .user-card {
    min-width: 240px;
  }

  .form-popup {
    min-width: 260px;
  }
}
</style>
