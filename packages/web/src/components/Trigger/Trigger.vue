<script setup lang="ts">
import {
  ref,
  computed,
  watch,
  nextTick,
  onMounted,
  onBeforeUnmount,
  Teleport,
  Transition,
  type CSSProperties
} from 'vue';
import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  arrow,
  hide,
  size,
  type Placement,
  type Strategy
} from '@floating-ui/vue';
import { getClassPrefix } from '@utils';
import type { TriggerProps, TriggerEmits, TriggerSlots, TriggerPosition, UTriggerEvent } from './interface';

const props = withDefaults(defineProps<TriggerProps>(), {
  trigger: 'hover',
  position: 'bottom',
  disabled: false,
  popupOffset: 6,
  showArrow: false,
  alignPoint: false,
  popupHoverStay: true,
  blurToClose: true,
  clickToClose: true,
  clickOutsideToClose: true,
  unmountOnClose: false,
  animationName: 'fade',
  mouseEnterDelay: 100,
  mouseLeaveDelay: 100,
  focusDelay: 0,
  autoFitPopupWidth: false,
  autoFitPopupMinWidth: false,
  autoFixPosition: true,
  updateAtScroll: false,
  autoFitPosition: true,
  renderToBody: true,
  preventFocus: false,
  scrollToClose: false,
  scrollToCloseDistance: 0,
});

const emit = defineEmits<TriggerEmits>();
defineSlots<TriggerSlots>();

const prefixCls = getClassPrefix('trigger');

// 引用元素
const triggerRef = ref<HTMLElement>();
const popupRef = ref<HTMLElement>();
const arrowRef = ref<HTMLElement>();

// 内部状态
const internalVisible = ref(props.defaultPopupVisible || false);
const mousePosition = ref<{ x: number; y: number } | null>(null);
const delayTimer = ref<number | null>(null);

// 计算属性
const computedVisible = computed(() => {
  return props.popupVisible !== undefined ? props.popupVisible : internalVisible.value;
});

const triggerMethods = computed(() => {
  const triggers = Array.isArray(props.trigger) ? props.trigger : [props.trigger];
  return triggers as UTriggerEvent[];
});

// 位置映射
const positionMap: Record<TriggerPosition, Placement> = {
  'top': 'top',
  'top-start': 'top-start',
  'top-end': 'top-end',
  'bottom': 'bottom',
  'bottom-start': 'bottom-start',
  'bottom-end': 'bottom-end',
  'left': 'left',
  'left-start': 'left-start',
  'left-end': 'left-end',
  'right': 'right',
  'right-start': 'right-start',
  'right-end': 'right-end',
};

const placement = computed(() => {
  return positionMap[props.position || 'bottom'];
});

// Floating UI 配置
const middleware = computed(() => {
  const middlewares = [
    offset(props.popupOffset),
  ];

  if (props.autoFitPosition) {
    middlewares.push(flip());
    middlewares.push(shift({ padding: 8 }));
  }

  if (props.showArrow && arrowRef.value) {
    middlewares.push(arrow({ element: arrowRef }));
  }

  middlewares.push(hide());

  if (props.autoFitPopupWidth || props.autoFitPopupMinWidth) {
    middlewares.push(
      size({
        apply({ availableWidth, elements }) {
          if (props.autoFitPopupWidth) {
            Object.assign(elements.floating.style, {
              width: `${availableWidth}px`,
            });
          }
          if (props.autoFitPopupMinWidth) {
            Object.assign(elements.floating.style, {
              minWidth: `${elements.reference.getBoundingClientRect().width}px`,
            });
          }
        },
      })
    );
  }

  return middlewares;
});

const strategy = computed<Strategy>(() => {
  return props.renderToBody ? 'fixed' : 'absolute';
});

// 使用 Floating UI
const { floatingStyles, middlewareData, update } = useFloating(
  triggerRef,
  popupRef,
  {
    placement,
    middleware,
    strategy,
    whileElementsMounted: props.autoFixPosition ? autoUpdate : undefined,
  }
);

// 箭头样式
const arrowStyles = computed(() => {
  const arrowData = middlewareData.value.arrow;
  if (!arrowData || !props.showArrow) return {};

  const { x, y } = arrowData;
  const staticSide = {
    top: 'bottom',
    right: 'left',
    bottom: 'top',
    left: 'right',
  }[placement.value.split('-')[0]];

  const arrowStyle: CSSProperties = {
    position: 'absolute',
    left: x != null ? `${x}px` : '',
    top: y != null ? `${y}px` : '',
    right: '',
    bottom: '',
    [staticSide as string]: '-4px',
  };

  return arrowStyle;
});

// 弹出框样式
const popupStyles = computed(() => {
  const styles: CSSProperties = {
    ...floatingStyles.value,
    ...props.popupStyle,
  };

  // 处理位置偏移
  if (props.popupTranslate) {
    const translate = Array.isArray(props.popupTranslate)
      ? props.popupTranslate
      : props.popupTranslate[props.position || 'bottom'];

    if (translate) {
      const [x, y] = translate;
      const currentTransform = styles.transform || '';
      styles.transform = `${currentTransform} translate(${x}px, ${y}px)`;
    }
  }

  return styles;
});

// 清除延迟定时器
const clearDelayTimer = () => {
  if (delayTimer.value) {
    clearTimeout(delayTimer.value);
    delayTimer.value = null;
  }
};

// 更新可见状态
const updateVisible = (visible: boolean, delay = 0) => {
  if (props.disabled) return;

  clearDelayTimer();

  if (delay > 0) {
    delayTimer.value = window.setTimeout(() => {
      changeVisible(visible);
    }, delay);
  } else {
    changeVisible(visible);
  }
};

// 改变可见状态
const changeVisible = (visible: boolean) => {
  if (computedVisible.value === visible) return;

  if (props.popupVisible === undefined) {
    internalVisible.value = visible;
  }

  emit('update:popupVisible', visible);
  emit('popup-visible-change', visible);

  if (visible) {
    nextTick(() => {
      update();
    });
  }
};

// 更新鼠标位置
const updateMousePosition = (e: MouseEvent) => {
  if (props.alignPoint) {
    mousePosition.value = { x: e.clientX, y: e.clientY };
  }
};

// 事件处理器
const handleClick = (e: MouseEvent) => {
  if (props.disabled) return;

  if (triggerMethods.value.includes('click')) {
    updateMousePosition(e);
    if (computedVisible.value && props.clickToClose) {
      updateVisible(false);
    } else if (!computedVisible.value) {
      updateVisible(true);
    }
  }
};

const handleMouseEnter = (e: MouseEvent) => {
  if (props.disabled || !triggerMethods.value.includes('hover')) return;

  updateMousePosition(e);
  updateVisible(true, props.mouseEnterDelay);
};

const handleMouseLeave = () => {
  if (props.disabled || !triggerMethods.value.includes('hover')) return;

  updateVisible(false, props.mouseLeaveDelay);
};

const handleFocus = (e: FocusEvent) => {
  if (props.disabled || !triggerMethods.value.includes('focus')) return;

  updateVisible(true, props.focusDelay);
};

const handleBlur = () => {
  if (props.disabled || !triggerMethods.value.includes('focus') || !props.blurToClose) return;

  updateVisible(false);
};

const handleContextMenu = (e: MouseEvent) => {
  if (props.disabled || !triggerMethods.value.includes('contextMenu')) return;

  e.preventDefault();
  updateMousePosition(e);
  updateVisible(!computedVisible.value);
};

// 弹出框鼠标事件
const handlePopupMouseEnter = () => {
  if (props.popupHoverStay && triggerMethods.value.includes('hover')) {
    clearDelayTimer();
  }
};

const handlePopupMouseLeave = () => {
  if (props.popupHoverStay && triggerMethods.value.includes('hover')) {
    updateVisible(false, props.mouseLeaveDelay);
  }
};

// 点击外部关闭
const handleClickOutside = (e: Event) => {
  if (!props.clickOutsideToClose || !computedVisible.value) return;

  const target = e.target as Node;
  const triggerEl = triggerRef.value;
  const popupEl = popupRef.value;

  if (
    triggerEl && !triggerEl.contains(target) &&
    popupEl && !popupEl.contains(target)
  ) {
    updateVisible(false);
  }
};

// 滚动关闭
const handleScroll = () => {
  if (props.scrollToClose && computedVisible.value) {
    updateVisible(false);
  }
};

// 生命周期
onMounted(() => {
  if (props.clickOutsideToClose) {
    document.addEventListener('click', handleClickOutside);
  }

  if (props.scrollToClose) {
    document.addEventListener('scroll', handleScroll, true);
  }
});

onBeforeUnmount(() => {
  clearDelayTimer();

  if (props.clickOutsideToClose) {
    document.removeEventListener('click', handleClickOutside);
  }

  if (props.scrollToClose) {
    document.removeEventListener('scroll', handleScroll, true);
  }
});

// 监听可见状态变化
watch(computedVisible, (visible) => {
  if (visible) {
    emit('show');
  } else {
    emit('hide');
  }
});

// 动画事件
const onEnter = () => {
  emit('show');
};

const onLeave = () => {
  emit('hide');
};

// 获取容器
const getContainer = () => {
  if (typeof props.popupContainer === 'string') {
    return document.querySelector(props.popupContainer) || document.body;
  }
  return props.popupContainer || document.body;
};
</script>

<template>
  <div :class="`${prefixCls}-wrapper`">
    <!-- 触发元素 -->
    <div
      ref="triggerRef"
      :class="prefixCls"
      @click="handleClick"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @focus="handleFocus"
      @blur="handleBlur"
      @contextmenu="handleContextMenu"
    >
      <slot />
    </div>

    <!-- 弹出框 -->
    <Teleport :to="getContainer()" :disabled="!props.renderToBody">
      <Transition
        :name="props.animationName"
        @enter="onEnter"
        @leave="onLeave"
      >
        <div
          v-if="computedVisible && (!props.unmountOnClose || computedVisible)"
          ref="popupRef"
          :class="[
            `${prefixCls}-popup`,
            props.contentClass
          ]"
          :style="popupStyles"
          @mouseenter="handlePopupMouseEnter"
          @mouseleave="handlePopupMouseLeave"
        >
          <!-- 箭头 -->
          <div
            v-if="props.showArrow"
            ref="arrowRef"
            :class="[
              `${prefixCls}-arrow`,
              props.arrowClass
            ]"
            :style="{ ...arrowStyles, ...props.arrowStyle }"
          />

          <!-- 弹出框内容 -->
          <div
            :class="`${prefixCls}-content`"
            :style="props.contentStyle"
          >
            <slot name="content" />
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>


