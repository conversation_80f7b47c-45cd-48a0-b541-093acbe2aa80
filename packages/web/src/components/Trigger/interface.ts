import type { CSSProperties } from 'vue';

export type UTriggerEvent = 'hover' | 'click' | 'focus' | 'contextMenu';
export type TriggerPosition =
  | 'top' | 'top-start' | 'top-end'
  | 'bottom' | 'bottom-start' | 'bottom-end'
  | 'left' | 'left-start' | 'left-end'
  | 'right' | 'right-start' | 'right-end';

export type TriggerPopupTranslate =
  | [number, number]
  | { [key in TriggerPosition]?: [number, number] };

export interface TriggerProps {
  /**
   * @zh 弹出框是否可见
   * @en Whether the popup is visible
   * @vModel
   */
  popupVisible?: boolean;
  /**
   * @zh 弹出框默认是否可见（非受控模式）
   * @en Whether the popup is visible by default (uncontrolled mode)
   */
  defaultPopupVisible?: boolean;
  /**
   * @zh 触发方式
   * @en Trigger method
   * @values 'hover','click','focus','contextMenu'
   */
  trigger?: UTriggerEvent | UTriggerEvent[];
  /**
   * @zh 弹出位置
   * @en Popup position
   */
  position?: TriggerPosition;
  /**
   * @zh 是否禁用
   * @en Whether to disable
   */
  disabled?: boolean;
  /**
   * @zh 弹出框偏移量
   * @en Popup offset
   */
  popupOffset?: number;
  /**
   * @zh 弹出框位置偏移
   * @en Popup position translate
   */
  popupTranslate?: TriggerPopupTranslate;
  /**
   * @zh 是否显示箭头
   * @en Whether to show arrow
   */
  showArrow?: boolean;
  /**
   * @zh 是否跟随鼠标位置
   * @en Whether to align with mouse position
   */
  alignPoint?: boolean;
  /**
   * @zh 鼠标移入弹出框时是否保持显示
   * @en Whether to keep showing when mouse enters popup
   */
  popupHoverStay?: boolean;
  /**
   * @zh 失去焦点时是否关闭
   * @en Whether to close when blur
   */
  blurToClose?: boolean;
  /**
   * @zh 点击时是否关闭
   * @en Whether to close when click
   */
  clickToClose?: boolean;
  /**
   * @zh 点击外部时是否关闭
   * @en Whether to close when click outside
   */
  clickOutsideToClose?: boolean;
  /**
   * @zh 关闭时是否卸载节点
   * @en Whether to unmount when close
   */
  unmountOnClose?: boolean;
  /**
   * @zh 弹出框内容的类名
   * @en Class name of popup content
   */
  contentClass?: any;
  /**
   * @zh 弹出框内容的样式
   * @en Style of popup content
   */
  contentStyle?: CSSProperties;
  /**
   * @zh 箭头的类名
   * @en Class name of arrow
   */
  arrowClass?: any;
  /**
   * @zh 箭头的样式
   * @en Style of arrow
   */
  arrowStyle?: CSSProperties;
  /**
   * @zh 弹出框的样式
   * @en Style of popup
   */
  popupStyle?: CSSProperties;
  /**
   * @zh 动画名称
   * @en Animation name
   */
  animationName?: string;
  /**
   * @zh 动画持续时间
   * @en Animation duration
   */
  duration?: number | {
    enter: number;
    leave: number;
  };
  /**
   * @zh 鼠标移入延迟时间
   * @en Mouse enter delay
   */
  mouseEnterDelay?: number;
  /**
   * @zh 鼠标移出延迟时间
   * @en Mouse leave delay
   */
  mouseLeaveDelay?: number;
  /**
   * @zh 获得焦点延迟时间
   * @en Focus delay
   */
  focusDelay?: number;
  /**
   * @zh 是否自动调整弹出框宽度
   * @en Whether to auto fit popup width
   */
  autoFitPopupWidth?: boolean;
  /**
   * @zh 是否自动调整弹出框最小宽度
   * @en Whether to auto fit popup min width
   */
  autoFitPopupMinWidth?: boolean;
  /**
   * @zh 当触发器的尺寸发生变化时，是否重新计算弹出框位置
   * @en Whether to recalculate popup position when trigger size changes
   */
  autoFixPosition?: boolean;
  /**
   * @zh 弹出框的挂载容器
   * @en Popup container
   */
  popupContainer?: string | HTMLElement;
  /**
   * @zh 是否在容器滚动时更新弹出框的位置
   * @en Whether to update popup position when container scrolls
   */
  updateAtScroll?: boolean;
  /**
   * @zh 是否自动调整弹出框位置，以适应窗口大小
   * @en Whether to auto fit popup position to window size
   */
  autoFitPosition?: boolean;
  /**
   * @zh 是否挂载在 body 元素下
   * @en Whether to render to body
   */
  renderToBody?: boolean;
  /**
   * @zh 是否阻止弹出层中的元素点击时获取焦点
   * @en Whether to prevent focus when click popup elements
   */
  preventFocus?: boolean;
  /**
   * @zh 是否在滚动时关闭弹出框
   * @en Whether to close popup when scroll
   */
  scrollToClose?: boolean;
  /**
   * @zh 滚动阈值，当滚动距离超过该值时触发关闭
   * @en Scroll threshold for closing popup
   */
  scrollToCloseDistance?: number;
}

export interface TriggerEmits {
  /**
   * @zh 弹出框显示状态改变时触发
   * @en Triggered when popup visible state changes
   */
  (e: 'popup-visible-change', visible: boolean): void;
  /**
   * @zh 弹出框显示后（动画结束）触发
   * @en Triggered after popup is shown (animation ends)
   */
  (e: 'show'): void;
  /**
   * @zh 弹出框隐藏后（动画结束）触发
   * @en Triggered after popup is hidden (animation ends)
   */
  (e: 'hide'): void;
  /**
   * @zh 更新 popupVisible 状态
   * @en Update popupVisible state
   */
  (e: 'update:popupVisible', visible: boolean): void;
}

export interface TriggerSlots {
  /**
   * @zh 默认插槽，触发元素
   * @en Default slot, trigger element
   */
  default?: () => any;
  /**
   * @zh 弹出框内容
   * @en Popup content
   */
  content?: () => any;
}
