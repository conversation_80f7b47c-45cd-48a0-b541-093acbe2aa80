@import "../../../assets/less/variables.less";

@trigger-prefix-cls: ~'@{u-prefix}-trigger';

.@{trigger-prefix-cls} {
  &-wrapper {
    display: inline-block;
  }

  &-popup {
    position: absolute;
    z-index: 1000;
    background: var(--u-bg-color-3, #ffffff);
    border: 1px solid var(--u-color-neutral-3, #e0e0e0);
    border-radius: var(--u-radius-default, 6px);
    box-shadow: var(--u-shadow-lg, 0 6px 16px 0 rgba(0, 0, 0, 0.08));
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.5;
    color: var(--u-text-color-primary, #333);

    // 隐藏状态
    &[data-hide] {
      visibility: hidden;
    }
  }

  &-arrow {
    position: absolute;
    z-index: -1;
    display: block;
    box-sizing: border-box;
    width: 8px;
    height: 8px;
    background-color: var(--u-bg-color-3, #ffffff);
    border: 1px solid var(--u-color-neutral-3, #e0e0e0);
    transform: rotate(45deg);

    // 不同位置的箭头样式
    .@{trigger-prefix-cls}-popup[data-placement^="top"] & {
      border-top: none;
      border-left: none;
    }

    .@{trigger-prefix-cls}-popup[data-placement^="bottom"] & {
      border-bottom: none;
      border-right: none;
    }

    .@{trigger-prefix-cls}-popup[data-placement^="left"] & {
      border-left: none;
      border-bottom: none;
    }

    .@{trigger-prefix-cls}-popup[data-placement^="right"] & {
      border-right: none;
      border-top: none;
    }
  }

  &-content {
    position: relative;
    z-index: 1;
  }
}

// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.zoom-enter-active,
.zoom-leave-active {
  transition: all 0.2s ease;
  transform-origin: center;
}

.zoom-enter-from,
.zoom-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.2s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  opacity: 0;
  transform: translateY(8px);
}

.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.2s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-8px);
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.2s ease;
}

.slide-left-enter-from,
.slide-left-leave-to {
  opacity: 0;
  transform: translateX(8px);
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.2s ease;
}

.slide-right-enter-from,
.slide-right-leave-to {
  opacity: 0;
  transform: translateX(-8px);
}

// 主题适配
html[theme-mode="dark"] {
  .@{trigger-prefix-cls} {
    &-popup {
      background: var(--u-bg-color-3, #2a2a2b);
      border-color: var(--u-color-neutral-3, #727273);
      color: var(--u-text-color-primary, #fff);
    }

    &-arrow {
      background-color: var(--u-bg-color-3, #2a2a2b);
      border-color: var(--u-color-neutral-3, #727273);
    }
  }
}
