```yaml
changelog: true
```

## 2.57.0

`2025-03-10`

### 💎 Enhancement

- fix re-throw error in onBeforeOk for proper error handling ([#3407](https://github.com/arco-design/arco-design-vue/pull/3407))


## 2.36.1

`2022-09-09`

### 💎 Enhancement

- The on-before-ok property supports function returning a Promise ([#1623](https://github.com/arco-design/arco-design-vue/pull/1623))


## 2.32.1

`2022-07-01`

### 💅 Style

- Fix the problem that the icons under different types are black by default ([#1366](https://github.com/arco-design/arco-design-vue/pull/1366))


## 2.18.0-beta.2

`2022-02-25`

### 💅 Style

- Optimize display animation ([#733](https://github.com/arco-design/arco-design-vue/pull/733))


## 2.10.1

`2021-12-14`

### 💅 Style

- Fix the component style problem, and adjust the default size of the button to `mini`, which is consistent with React ([#390](https://github.com/arco-design/arco-design-vue/pull/390))


## 2.7.0

`2021-11-26`

### 🆕 Feature

- Added `on-before-ok` and `on-before-cancel` property events ([#229](https://github.com/arco-design/arco-design-vue/pull/229))


## 2.1.1

`2021-11-08`

### 🐛 BugFix

- Fix the problem that the parameters of the `ok/cancel` button are lost ([#105](https://github.com/arco-design/arco-design-vue/pull/105))

