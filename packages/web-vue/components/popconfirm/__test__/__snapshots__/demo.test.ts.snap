// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<popconfirm> demo: render [async] correctly 1`] = `
"<button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
  <!--v-if-->Click To Show
</button>"
`;

exports[`<popconfirm> demo: render [basic] correctly 1`] = `
"<button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
  <!--v-if-->Click To Delete
</button>"
`;

exports[`<popconfirm> demo: render [custom] correctly 1`] = `
"<button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
  <!--v-if-->Discard
</button>"
`;

exports[`<popconfirm> demo: render [position] correctly 1`] = `
"<div style=\\"position: relative; width: 440px; height: 280px;\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal button\\" type=\\"button\\" style=\\"position: absolute; top: 0px; left: 70px;\\">
    <!--v-if-->TL
  </button>
  <!----><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal button\\" type=\\"button\\" style=\\"position: absolute; top: 0px; left: 180px;\\">
    <!--v-if-->TOP
  </button>
  <!----><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal button\\" type=\\"button\\" style=\\"position: absolute; top: 0px; left: 290px;\\">
    <!--v-if-->TR
  </button>
  <!----><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal button\\" type=\\"button\\" style=\\"position: absolute; top: 240px; left: 70px;\\">
    <!--v-if-->BL
  </button>
  <!----><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal button\\" type=\\"button\\" style=\\"position: absolute; top: 240px; left: 180px;\\">
    <!--v-if-->BOTTOM
  </button>
  <!----><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal button\\" type=\\"button\\" style=\\"position: absolute; top: 240px; left: 290px;\\">
    <!--v-if-->BR
  </button>
  <!----><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal button\\" type=\\"button\\" style=\\"position: absolute; top: 60px; left: 10px;\\">
    <!--v-if-->LT
  </button>
  <!----><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal button\\" type=\\"button\\" style=\\"position: absolute; top: 120px; left: 10px;\\">
    <!--v-if-->LEFT
  </button>
  <!----><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal button\\" type=\\"button\\" style=\\"position: absolute; top: 180px; left: 10px;\\">
    <!--v-if-->LB
  </button>
  <!----><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal button\\" type=\\"button\\" style=\\"position: absolute; top: 60px; left: 350px;\\">
    <!--v-if-->RT
  </button>
  <!----><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal button\\" type=\\"button\\" style=\\"position: absolute; top: 120px; left: 350px;\\">
    <!--v-if-->RIGHT
  </button>
  <!----><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal button\\" type=\\"button\\" style=\\"position: absolute; top: 180px; left: 350px;\\">
    <!--v-if-->RB
  </button>
  <!---->
</div>"
`;

exports[`<popconfirm> demo: render [type] correctly 1`] = `
"<div class=\\"arco-space arco-space-horizontal arco-space-align-center\\">
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if-->Click To Delete
    </button>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if-->Click To Delete
    </button>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\" style=\\"margin-right: 8px;\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if-->Click To Delete
    </button>
    <!---->
  </div>
  <!---->
  <div class=\\"arco-space-item\\"><button class=\\"arco-btn arco-btn-secondary arco-btn-shape-square arco-btn-size-medium arco-btn-status-normal\\" type=\\"button\\">
      <!--v-if-->Click To Delete
    </button>
    <!---->
  </div>
</div>"
`;
